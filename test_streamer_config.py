#!/usr/bin/env python3
"""
Test script to verify the new streamer mode configuration options work correctly.
"""

import json
import sys
import os

def get_default_config():
    """Get the default config without importing the module (to avoid dependency issues)"""
    return {
        "cooldown": 10,
        "port": 1100,
        "weapon": "Vandal",
        "chat_limit": 5,
        "table": {
            "skin": True,
            "rr": True,
            "earned_rr": True,
            "peakrank": True,
            "previousrank": False,
            "leaderboard": True,
            "headshot_percent": True,
            "winrate": True,
            "kd": False,
            "level": True
        },
        "flags": {
            "last_played": True,
            "auto_hide_leaderboard": True,
            "pre_cls": False,
            "game_chat": True,
            "peak_rank_act": True,
            "discord_rpc": True,
            "aggregate_rank_rr": True,
            "show_streamer_names": True,
            "streamer_mode_indicator": True
        }
    }

def test_default_config():
    """Test that the new flags are present in DEFAULT_CONFIG"""
    print("Testing DEFAULT_CONFIG...")

    # Check if the new flags exist
    DEFAULT_CONFIG = get_default_config()
    flags = DEFAULT_CONFIG.get("flags", {})
    
    required_flags = ["show_streamer_names", "streamer_mode_indicator"]
    
    for flag in required_flags:
        if flag in flags:
            print(f"✓ {flag}: {flags[flag]}")
        else:
            print(f"✗ {flag}: MISSING")
            return False
    
    return True

def test_config_creation():
    """Test creating a config file with the new options"""
    print("\nTesting config file creation...")

    # Create a test config
    DEFAULT_CONFIG = get_default_config()
    test_config = DEFAULT_CONFIG.copy()
    
    # Save to test file
    with open("test_config.json", "w") as f:
        json.dump(test_config, f, indent=4)
    
    # Read it back
    with open("test_config.json", "r") as f:
        loaded_config = json.load(f)
    
    # Verify the flags are present
    flags = loaded_config.get("flags", {})
    
    if "show_streamer_names" in flags and "streamer_mode_indicator" in flags:
        print("✓ Config file created successfully with new flags")
        
        # Clean up
        os.remove("test_config.json")
        return True
    else:
        print("✗ Config file missing new flags")
        return False

def test_colors_method():
    """Test that the colors.py file has the new method"""
    print("\nTesting colors.py file...")

    try:
        # Read the colors.py file to check for the new method
        with open("src/colors.py", "r") as f:
            content = f.read()

        if "add_streamer_mode_indicator" in content:
            print("✓ add_streamer_mode_indicator method found in colors.py")

            if "[S]" in content:
                print("✓ Streamer mode indicator [S] found in method")
                return True
            else:
                print("✗ Streamer mode indicator [S] not found")
                return False
        else:
            print("✗ add_streamer_mode_indicator method not found")
            return False

    except FileNotFoundError:
        print("✗ colors.py file not found")
        return False
    except Exception as e:
        print(f"✗ Error reading colors.py: {e}")
        return False

if __name__ == "__main__":
    print("VALORANT Rank Yoinker - Streamer Mode Features Test")
    print("=" * 50)
    
    tests = [
        test_default_config,
        test_config_creation,
        test_colors_method
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The streamer mode features are ready to use.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please check the implementation.")
        sys.exit(1)
