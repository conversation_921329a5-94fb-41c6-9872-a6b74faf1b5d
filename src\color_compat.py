"""
Compatibility module to replace colr functionality with colorama for Python 3.13+ compatibility
"""

import colorama
from colorama import Fore, Back, Style

# Initialize colorama
colorama.init(autoreset=True)

def color(text, fore=None, back=None, style=None):
    """
    Simple color function to replace colr.color functionality
    
    Args:
        text: Text to colorize
        fore: Foreground color as RGB tuple (r, g, b) or None
        back: Background color as RGB tuple (r, g, b) or None  
        style: Style (not used in this simple implementation)
    
    Returns:
        Colored text string
    """
    if fore is None:
        return str(text)
    
    # Convert RGB to closest ANSI color
    r, g, b = fore
    
    # Simple mapping of RGB ranges to ANSI colors
    if r > 200 and g < 100 and b < 100:  # Red-ish
        color_code = Fore.RED
    elif r < 100 and g > 200 and b < 100:  # Green-ish
        color_code = Fore.GREEN
    elif r < 100 and g < 100 and b > 200:  # Blue-ish
        color_code = Fore.BLUE
    elif r > 200 and g > 200 and b < 100:  # Yellow-ish
        color_code = Fore.YELLOW
    elif r > 200 and g < 100 and b > 200:  # Magenta-ish
        color_code = Fore.MAGENTA
    elif r < 100 and g > 200 and b > 200:  # Cyan-ish
        color_code = Fore.CYAN
    elif r > 200 and g > 200 and b > 200:  # White-ish
        color_code = Fore.WHITE
    elif r < 100 and g < 100 and b < 100:  # Black-ish
        color_code = Fore.BLACK
    else:  # Default to white for other colors
        color_code = Fore.WHITE
    
    return f"{color_code}{text}{Style.RESET_ALL}"
